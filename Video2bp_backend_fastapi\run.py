#!/usr/bin/env python3
"""
自动化启动脚本
确保在正确的虚拟环境中运行FastAPI应用
"""

import os
import sys
import subprocess
from pathlib import Path


def check_virtual_env():
    """检查是否在虚拟环境中"""
    return hasattr(sys, 'real_prefix') or (
        hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
    )


def create_and_activate_venv():
    """创建并激活虚拟环境"""
    venv_path = Path("fastapi_env")
    
    if not venv_path.exists():
        print("🔧 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", "fastapi_env"], check=True)
        print("✅ Virtual environment created")
    
    # 检查依赖是否已安装
    if sys.platform == "win32":
        pip_path = venv_path / "Scripts" / "pip.exe"
        python_path = venv_path / "Scripts" / "python.exe"
    else:
        pip_path = venv_path / "bin" / "pip"
        python_path = venv_path / "bin" / "python"
    
    # 检查FastAPI是否已安装
    try:
        result = subprocess.run([
            str(python_path), "-c", "import fastapi; print('FastAPI installed')"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("📦 Installing dependencies...")
            subprocess.run([
                str(pip_path), "install", 
                "fastapi==0.100.1", "pydantic==2.0.3", "uvicorn==0.23.2"
            ], check=True)
            
            if Path("requirements.txt").exists():
                subprocess.run([
                    str(pip_path), "install", "-r", "requirements.txt"
                ], check=True)
            
            print("✅ Dependencies installed")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False
    
    return str(python_path)


def main():
    """主函数"""
    print("🚀 Video2BP FastAPI Launcher")
    print("=" * 40)
    
    if check_virtual_env():
        print("✅ Already in virtual environment")
        # 直接启动应用
        from main import app
        import uvicorn
        
        print("🌟 Starting FastAPI server...")
        uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)
    else:
        print("⚠️  Not in virtual environment")
        python_path = create_and_activate_venv()
        
        if python_path:
            print("🌟 Starting FastAPI server in virtual environment...")
            subprocess.run([
                python_path, "-c",
                "from main import app; import uvicorn; uvicorn.run(app, host='127.0.0.1', port=8000, reload=True)"
            ])
        else:
            print("❌ Failed to set up virtual environment")
            sys.exit(1)


if __name__ == "__main__":
    main()
