@echo off
echo 🚀 Video2BP FastAPI Launcher (Windows)
echo ========================================

REM 检查虚拟环境是否存在
if not exist "fastapi_env" (
    echo 🔧 Creating virtual environment...
    python -m venv fastapi_env
    echo ✅ Virtual environment created
)

REM 激活虚拟环境
echo 🔄 Activating virtual environment...
call fastapi_env\Scripts\activate.bat

REM 检查并安装依赖
echo 📦 Checking dependencies...
python -c "import fastapi" 2>nul
if errorlevel 1 (
    echo 📦 Installing dependencies...
    pip install fastapi==0.100.1 pydantic==2.0.3 uvicorn==0.23.2
    if exist requirements.txt (
        pip install -r requirements.txt
    )
    echo ✅ Dependencies installed
)

REM 启动应用
echo 🌟 Starting FastAPI server...
python main.py

pause
