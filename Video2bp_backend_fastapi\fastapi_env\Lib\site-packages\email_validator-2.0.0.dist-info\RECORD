../../Scripts/email_validator.exe,sha256=7gWmF8fczwD1Yhn-IeZZvFI75weDckU5_oRKqkHQjo8,108418
email_validator-2.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
email_validator-2.0.0.dist-info/LICENSE,sha256=ogEPNDSH0_dhiv_lT3ifVIdgIzHAqNA_SemnxUfPBJk,7048
email_validator-2.0.0.dist-info/METADATA,sha256=NWdhnwGQ4DOR1shA0aT9dCkOBiIqKdpaZ4yd4jRcaEs,25407
email_validator-2.0.0.dist-info/RECORD,,
email_validator-2.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
email_validator-2.0.0.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
email_validator-2.0.0.dist-info/entry_points.txt,sha256=DiP9magTC_pGdQ7tDRDKP7i3l8-2b8GgwF0APpq8w54,57
email_validator-2.0.0.dist-info/top_level.txt,sha256=fYDOSWFZke46ut7WqdOAJjjhlpPYAaOwOwIsh3s8oWI,16
email_validator/__init__.py,sha256=4XQoz2JDUsvdDuvOqU7LV23QJrqwqYfycv4sVBK6BqA,4189
email_validator/__main__.py,sha256=SgarDcfH3W5KlcuUi6aaiQPqMdL3C-mOZVnTS6WesS4,2146
email_validator/__pycache__/__init__.cpython-311.pyc,,
email_validator/__pycache__/__main__.cpython-311.pyc,,
email_validator/__pycache__/deliverability.cpython-311.pyc,,
email_validator/__pycache__/exceptions_types.cpython-311.pyc,,
email_validator/__pycache__/rfc_constants.cpython-311.pyc,,
email_validator/__pycache__/syntax.cpython-311.pyc,,
email_validator/__pycache__/validate_email.cpython-311.pyc,,
email_validator/deliverability.py,sha256=wNcSvP4wJTuE5JmtSd56X8Nln1DkZ40RRsqBDobevQ0,5749
email_validator/exceptions_types.py,sha256=ruD1Xz-BLuagbfihbwSqFDNf9AHpfUwjG1oryoMwh6k,5524
email_validator/rfc_constants.py,sha256=2yJVQgKFaVcG2CV-XT4PWevBUjAPVHRycgzOpBaqRZE,2720
email_validator/syntax.py,sha256=qr4QaQW_wP-_KR54ZDGjKeZYC9jx_GZ5X8sTX60RMf4,23969
email_validator/validate_email.py,sha256=CmNL3BRmXihfz_qluQG0jFkrgGCnG7mugrYJuy0YXKI,9037
