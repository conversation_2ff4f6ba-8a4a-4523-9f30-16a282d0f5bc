#!/bin/bash

echo "🚀 Video2BP FastAPI Launcher (Linux/Mac)"
echo "========================================"

# 检查虚拟环境是否存在
if [ ! -d "fastapi_env" ]; then
    echo "🔧 Creating virtual environment..."
    python3 -m venv fastapi_env
    echo "✅ Virtual environment created"
fi

# 激活虚拟环境
echo "🔄 Activating virtual environment..."
source fastapi_env/bin/activate

# 检查并安装依赖
echo "📦 Checking dependencies..."
if ! python -c "import fastapi" 2>/dev/null; then
    echo "📦 Installing dependencies..."
    pip install fastapi==0.100.1 pydantic==2.0.3 uvicorn==0.23.2
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    fi
    echo "✅ Dependencies installed"
fi

# 启动应用
echo "🌟 Starting FastAPI server..."
python main.py
