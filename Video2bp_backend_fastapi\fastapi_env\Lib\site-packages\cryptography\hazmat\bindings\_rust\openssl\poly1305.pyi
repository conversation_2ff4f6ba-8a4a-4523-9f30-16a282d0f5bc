# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from cryptography.utils import Buffer

class Poly1305:
    def __init__(self, key: <PERSON>uffer) -> None: ...
    @staticmethod
    def generate_tag(key: <PERSON><PERSON><PERSON>, data: <PERSON><PERSON><PERSON>) -> bytes: ...
    @staticmethod
    def verify_tag(key: <PERSON><PERSON><PERSON>, data: Buffer, tag: bytes) -> None: ...
    def update(self, data: Buffer) -> None: ...
    def finalize(self) -> bytes: ...
    def verify(self, tag: bytes) -> None: ...
