# Video2BP FastAPI Backend

Flask到FastAPI的完整迁移项目，保持API兼容性的同时提供现代化的异步支持。

## 🚀 快速开始

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Redis 6.0+

### 安装依赖
⚠️ **重要**: 必须使用虚拟环境，否则会有Pydantic版本冲突问题！

```bash
# 1. 创建虚拟环境（每次都要执行）
python -m venv fastapi_env

# 2. 激活虚拟环境
source fastapi_env/bin/activate  # Linux/Mac
# 或 fastapi_env\Scripts\activate  # Windows

# 3. 安装依赖
pip install fastapi==0.100.1 pydantic==2.0.3 uvicorn==0.23.2
pip install -r requirements.txt
```

### 配置环境变量
创建 `.env` 文件：
```env
DB_CONNECTION=mysql+pymysql://user:password@localhost:3306/database
ASYNC_DB_CONNECTION=mysql+aiomysql://user:password@localhost:3306/database
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key
DEBUG=True
```

### 启动服务
⚠️ **确保在虚拟环境中运行**

```bash
# 确认虚拟环境已激活（命令行前缀应显示 (fastapi_env)）
python main.py
```

访问 http://localhost:8000/docs 查看API文档

## 📋 API模块

### 用户认证 (`/api/user/`)
- `POST /send_verification_code` - 发送邮箱验证码
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `GET /logout` - 用户登出
- `POST /info` - 获取用户信息

### 视频管理 (`/api/video/`)
- `POST /upload` - 视频上传
- `POST /list` - 视频列表查询
- `GET /download` - 视频下载
- `GET /bp/download` - 骨骼点文件下载
- `GET /download/all` - 打包下载
- `POST /upload_chunk` - 分片上传
- `POST /merge_chunks` - 合并分片

### 首页管理 (`/api/`)
- `GET /compony/info` - 公司信息
- `GET /combo/info` - 套餐信息
- `POST /news/list` - 新闻列表
- `GET /news/detail` - 新闻详情

### 其他
- `POST /api/order/create` - 创建订单
- `GET /api/monitor` - 系统监控

## 🗃️ 数据库表结构

### users (用户表)
- `id`, `email`, `username`, `password`, `phone`
- `vip_level`, `status`, `created_at`, `updated_at`

### user_videos (用户视频表)
- `id`, `user_id`, `filename`, `bp_filename`
- `status`, `created_at`, `updated_at`

### news (新闻表)
- `id`, `title`, `content`, `cover`, `author`
- `type`, `is_display`, `is_pinned`, `created_at`, `updated_at`

## 🔧 技术特性

- **异步支持**: 全面使用 async/await
- **类型安全**: Pydantic 模型验证
- **自动文档**: Swagger UI 和 ReDoc
- **Cookie认证**: 与Flask版本兼容的认证方式
- **文件上传**: 支持分片上传和合并
- **数据库**: SQLAlchemy 异步ORM
- **缓存**: Redis 集成
- **生命周期管理**: 自动初始化和清理资源

## 📁 项目结构

```
Video2bp_backend_fastapi/
├── apps/                   # 应用模块
│   ├── app_user_auth/     # 用户认证
│   ├── app_video_manage/  # 视频管理
│   ├── app_home_manage/   # 首页管理
│   ├── app_order_manage/  # 订单管理
│   └── app_monitor/       # 系统监控
├── api/routes/            # 路由配置
├── core/                  # 核心配置
├── lib/                   # 工具库
├── models/                # 数据库模型
├── schemas/               # Pydantic模型
├── main.py               # 应用入口
└── requirements.txt      # 依赖文件
```

## ⚠️ 注意事项

1. **兼容性**: 与原Flask项目数据库完全兼容
2. **认证**: 使用Cookie认证，保持前端兼容性
3. **密码**: 使用MD5加密（与Flask版本一致）
4. **文件存储**: 需要创建 `static/videos` 和 `static/bp` 目录

## 🔍 故障排除

### Pydantic版本问题
如果遇到 `'not' is not a valid parameter name` 错误：
1. 创建新的虚拟环境
2. 安装指定版本: `pip install fastapi==0.100.1 pydantic==2.0.3`

### 数据库连接问题
1. 确保MySQL服务运行
2. 检查连接字符串格式
3. 确认数据库用户权限

### Redis连接问题
1. 确保Redis服务运行
2. 检查Redis URL配置
3. 测试连接: `redis-cli ping`

## 📚 更多文档

详细文档请查看 `md/` 目录：
- `MIGRATION_SUMMARY.md` - 迁移详情
- `DATABASE_SCHEMA.md` - 数据库结构
- `INSTALL_GUIDE.md` - 安装指南
